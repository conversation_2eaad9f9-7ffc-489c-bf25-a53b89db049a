package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.query.CardBinQuery;
import top.continew.admin.biz.model.req.CardBinBatchReq;
import top.continew.admin.biz.model.req.CardBinReq;
import top.continew.admin.biz.model.resp.CardBinDetailResp;
import top.continew.admin.biz.model.resp.CardBinResp;
import top.continew.admin.biz.service.CardBinService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 卡头管理管理 API
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Tag(name = "卡头管理管理 API")
@RestController
@CrudRequestMapping(value = "/biz/cardBin", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CardBinController extends BaseController<CardBinService, CardBinResp, CardBinDetailResp, CardBinQuery, CardBinReq> {


    @PostMapping("/sync")
    @Operation(summary = "同步卡头数据", description = "同步卡头数据")
    public void sync(CardPlatformEnum cardPlatform) {
        baseService.syncCardBin(cardPlatform);
    }


    @PutMapping("/batchEnable")
    @Operation(summary = "批量启用/禁用卡头", description = "批量启用/禁用卡头")
    public void batchEnable(@RequestBody CardBinBatchReq req) {
        baseService.batchEnable(req);
    }


}