package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.query.BusinessManagerItemQuery;
import top.continew.admin.biz.model.req.BmItemReq;
import top.continew.admin.biz.model.req.BusinessManagerItemReq;
import top.continew.admin.biz.model.req.BusinessManagerUpdateBMID;
import top.continew.admin.biz.model.req.BusinessManagerUpdateBMType;
import top.continew.admin.biz.model.resp.BusinessManagerItemDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerItemResp;
import top.continew.admin.biz.service.BusinessManagerItemService;
import top.continew.admin.biz.service.BusinessManagerService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * bm坑位管理 API
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
@Tag(name = "bm坑位管理 API")
@RestController
@CrudRequestMapping(value = "/biz/businessManagerItem", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
@RequiredArgsConstructor
public class BusinessManagerItemController extends BaseController<BusinessManagerItemService, BusinessManagerItemResp, BusinessManagerItemDetailResp, BusinessManagerItemQuery, BusinessManagerItemReq> {

    @PostMapping("/addBmItem")
    public void addBmItem(@RequestBody @Validated BmItemReq bmItemReq) {
        baseService.addBusinessManagerItem(bmItemReq);
    }

    @PostMapping("/updateBmId")
    public void updateBmId(@RequestBody  @Validated BusinessManagerUpdateBMID req) {
        baseService.updateBmId(req);
    }

    @PostMapping("/updateType")
    public void updateBmType(@RequestBody  @Validated BusinessManagerUpdateBMType req) {
        baseService.updateBmType(req);
    }
}