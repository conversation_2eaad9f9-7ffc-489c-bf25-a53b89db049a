package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.entity.BusinessManagerItemDO;
import top.continew.admin.biz.model.query.BusinessManagerItemQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.BusinessManagerItemDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerItemResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * bm坑位业务接口
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
public interface BusinessManagerItemService extends BaseService<BusinessManagerItemResp, BusinessManagerItemDetailResp, BusinessManagerItemQuery, BusinessManagerItemReq>, IService<BusinessManagerItemDO> {

    void addBusinessManagerItem(BmItemReq bmItemReq);

    void addBusinessManagerItem(BusinessManagerReq req, BusinessManagerDO businessManager);


    void banBusinessManager(Long businessManagerId);


    void updatePrice(BusinessManagerDO businessManager);

    void updateBmId(BusinessManagerUpdateBMID req);

    void updateBmType(BusinessManagerUpdateBMType req);
}