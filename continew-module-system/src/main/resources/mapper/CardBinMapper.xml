<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CardBinMapper">
    <sql id="base_sql">
        SELECT bcb.id,
               bcb.card_bin,
               bcb.name,
               bcb.platform,
               bcb.card_scheme,
               bcb.enable,
               bcb.create_time,
               COALESCE(CombinedCardStats.active_card_count, 0)        AS activeCardCount,
               COALESCE(CombinedCardStats.frozen_card_count, 0)        AS frozenCardCount,
               COALESCE(CombinedCardStats.recent_active_card_count, 0) AS recentActiveCardCount,
               COALESCE(CombinedCardStats.recent_frozen_card_count, 0) AS recentFrozenCardCount,
               COALESCE(CombinedCardStats.usedCount, 0)                AS usedCount
        FROM biz_card_bin bcb
                 LEFT JOIN (SELECT bc.bin_id,
                                   -- 总活跃卡数
                                   SUM(CASE WHEN bc.status = 1 THEN 1 ELSE 0 END)                     AS active_card_count,
                                   -- 总冻结卡数
                                   SUM(CASE WHEN bc.status = 3 THEN 1 ELSE 0 END)                     AS frozen_card_count,
                                   -- 近7天活跃卡数
                                   SUM(CASE
                                           WHEN bc.status = 1 AND bc.open_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1
                                           ELSE 0 END)                                                AS recent_active_card_count,
                                   -- 近7天冻结卡数
                                   SUM(CASE
                                           WHEN bc.status = 3 AND bc.open_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1
                                           ELSE 0 END)                                                AS recent_frozen_card_count,
                                   -- 使用数
                                   SUM(CASE WHEN bc.status = 1 AND bc.has_used = 1 THEN 1 ELSE 0 END) AS usedCount
                            FROM biz_card bc
                            GROUP BY bc.bin_id) AS CombinedCardStats ON bcb.id = CombinedCardStats.bin_id
        <where>
            <if test="query.cardBin != null and query.cardBin != ''">
                AND bcb.card_bin LIKE CONCAT('%', #{query.cardBin}, '%')
            </if>
            <if test="query.platform != null">
                AND bcb.platform = #{query.platform}
            </if>
            <if test="query.enable != null">
                AND bcb.enable = #{query.enable}
            </if>
        </where>
        ORDER BY bcb.create_time DESC
    </sql>
    <select id="customerPage" resultType="top.continew.admin.biz.model.resp.CardBinResp">
        <include refid="base_sql" />
    </select>
    <select id="selectTransAmount" resultType="top.continew.admin.biz.model.resp.CardBinResp">
        select
            COALESCE(-SUM(bct.trans_amount), 0) as totalTransactionAmount,
            COALESCE(-SUM(CASE
                WHEN bct.stat_time >= DATE_SUB(NOW(), INTERVAL #{day} DAY)
                THEN bct.trans_amount
                ELSE 0
            END), 0) as recentTransactionAmount
        from biz_card bc
                 left join biz_card_transaction bct on bc.card_number = bct.card_number
        where bc.bin_id = #{binId}
          and bct.trans_status != 4
    </select>
    <select id="customList" resultType="top.continew.admin.biz.model.resp.CardBinResp">
        <include refid="base_sql" />
    </select>
    <select id="selectAvailableCardCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM biz_card WHERE bin_id = #{id}
    </select>

</mapper>